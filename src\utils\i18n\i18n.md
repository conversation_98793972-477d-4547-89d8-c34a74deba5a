# 本地化管理工具

本模块提供了 VSCode 扩展的国际化支持功能,用于管理和使用本地化消息。

## 安装配置

本地化文件应放置在扩展的`i18n`目录下,以语言代码命名,如`en.json`、`zh-cn.json`等。

示例配置文件 `zh-cn.json`:

```json
{
  "hello": "你好",
  "dialog": {
    "confirm": "确认",
    "cancel": "取消"
  },
  "welcome": "欢迎 {0}!"
}
```

## 初始化

在扩展激活时进行初始化:

```typescript
import * as vscode from "vscode";
import { initializeLocalization } from "./LocalizationManager";

export function activate(context: vscode.ExtensionContext) {
  // 初始化本地化,会自动根据VSCode当前语言环境加载对应的语言文件
  // 如果找不到对应语言的文件,会fallback到en.json
  initializeLocalization(context);
}
```

## 基本使用

### 获取简单消息

```typescript
import { getMessage } from "./LocalizationManager";

// 获取顶层key
const text = getMessage("hello"); // "你好"

// 获取嵌套key (使用点号分隔)
const btnText = getMessage("dialog.confirm"); // "确认"
```

### 带参数的消息

```typescript
import { formatMessage } from "./LocalizationManager";

// 替换参数占位符 {0}, {1} 等
const msg = formatMessage("welcome", ["Alice"]); // "欢迎 Alice!"

// 参数可以为空或undefined,会被替换为空字符串
const safeMsg = formatMessage("welcome", [undefined]); // "欢迎 !"
```

## 错误处理

```typescript
// 找不到的key会返回key本身作为fallback
const missing = getMessage("nonexistent"); // "nonexistent"

// 格式化消息时的错误也会返回key本身
const badFormat = formatMessage("hello", []); // "hello"
```

## 高级功能

### 验证消息

用于检查消息 key 是否都存在:

```typescript
import { validateMessages } from "./LocalizationManager";

// 返回不存在的key列表
const missingKeys = validateMessages(["hello", "nonexistent"]);
console.log(missingKeys); // ['nonexistent']
```

### 重复 Key 检查

在初始化时会自动检查重复 key 并在控制台输出警告。可以处理嵌套的重复 key:

```json
{
  "key": "value1",
  "nested": {
    "key": "value2" // 会检测到重复: "nested.key"
  }
}
```

### 清除缓存

在需要重新加载本地化文件时使用:

```typescript
import { clearLocalizationCache } from "./LocalizationManager";

clearLocalizationCache();
```

## 最佳实践

1. 使用层级结构组织消息 key,便于管理
2. 保持 key 命名简单直观,使用小写字母和点号
3. 参数占位符从 0 开始编号: {0}, {1}
4. 在开发时使用 validateMessages()检查 key 完整性
5. 建议将所有使用的 key 定义为常量:

```typescript
// messageKeys.ts
export const MESSAGE_KEYS = {
  HELLO: "hello",
  DIALOG: {
    CONFIRM: "dialog.confirm",
    CANCEL: "dialog.cancel",
  },
} as const;
```
