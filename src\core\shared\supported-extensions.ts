const extensions = [
  "tla",
  "js",
  "jsx",
  "ts",
  "vue",
  "tsx",
  "py",
  // Rust
  "rs",
  "go",
  // <PERSON>
  "c",
  "h",
  // C++
  "cpp",
  "hpp",
  // C#
  "cs",
  // <PERSON>
  "rb",
  "java",
  "php",
  "swift",
  // Solidity
  "sol",
  // <PERSON><PERSON><PERSON>
  "kt",
  "kts",
  // Elixir
  "ex",
  "exs",
  // Elisp
  "el",
  // HTML
  "html",
  "htm",
  // Markdown
  "md",
  "markdown",
  // JSON
  "json",
  // CSS
  "css",
  // SystemRDL
  "rdl",
  // OCaml
  "ml",
  "mli",
  // <PERSON>a
  "lua",
  // <PERSON>ala
  "scala",
  // TOML
  "toml",
  // Zig
  "zig",
  // <PERSON>
  "elm",
  // Embedded Template
  "ejs",
  "erb",
].map((e) => `.${e}`);

export { extensions };

// Filter out markdown extensions for the scanner
export const scannerExtensions = extensions.filter(
  (ext) => ext !== ".md" && ext !== ".markdown"
);
