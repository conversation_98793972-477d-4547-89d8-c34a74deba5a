# OWNERSHIP

This file is provided as proof of ownership and authorship for the VS Code extension **`dish-ai-commit`**.

- **GitHub**: @littleCareless  
- **Repository**: https://github.com/littleCareless/dish-ai-commit  
- **VS Code Marketplace** (verified publisher): https://marketplace.visualstudio.com/items?itemName=littleCareless.dish-ai-commit  

---

## 📌 Declaration of Authorship

I, **@littleCareless**, am the original author, maintainer, and publisher of this extension.  
I created the `dish-ai-commit` codebase, its design, and the published package. All commits and releases under this namespace are under my direct control.

---

## 🛠 Purpose of This File

This `OWNERSHIP.md` file is created to support my claim for the `littleCareless` namespace ownership on **Open VSX**. It confirms:

1. **I am the genuine developer and publisher** associated with this editor extension.
2. **I control both the GitHub repository and the VS Code Marketplace listing**, which is verified under my personal account.
3. **This file's existence acts as tangible evidence**, reducing the need for further verification steps.

Please consider this documentation as auxiliary proof in conjunction with:
- An issue submitted to `EclipseFdn/open-vsx.org` to **claim namespace**.
- Full control of the Marketplace listing and source repo.

---

Thank you for reviewing this ownership documentation, which I hope will expedite the namespace verification process.

— @littleCareless