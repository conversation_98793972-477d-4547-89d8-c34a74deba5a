{"openai.apiKey.missing": "OpenAI API Key is not configured. Would you like to configure it now?", "ollama.baseUrl.missing": "Ollama Base URL is not configured. Would you like to configure it now?", "diff.simplification.warning": "Diff simplification is enabled. This may affect the accuracy of AI-generated commit messages. For more accurate results, consider disabling this feature.", "command.execution.failed": "Command execution failed", "scm.not.detected": "No supported version control system detected", "no.changes": "No changes to commit", "diff.too.long": "Changes are too long and exceed the model's maximum limit. Please reduce the number of selected files or content length.\nCurrent length: {0} characters\nMaximum limit: {1} characters", "commit.message.generated": "Commit message generated successfully (by {1} - {2})", "commit.message.write.failed": "Failed to write commit message to SCM input box: {0}", "commit.message.copied": "Commit message has been copied to clipboard. Please paste it into your version control system's commit input box", "commit.message.copy.failed": "Failed to copy commit message: {0}", "commit.message.manual.copy": "Commit message has been generated, please manually copy to commit box: \n {0}", "generate.commit.failed": "Failed to generate commit message: {0}", "get.models.failed": "Failed to get model list", "openai.config.required": "OpenAI API configuration is required to use this feature. Would you like to configure it now?", "openai.baseUrl.prompt": "Please enter OpenAI API URL", "openai.baseUrl.placeholder": "Example: https://api.openai.com/v1", "openai.apiKey.prompt": "Please enter OpenAI API Key", "ai.model.picker.title": "Select AI Model", "ai.model.picker.placeholder": "Select AI model for generating commit messages", "model.update.success": "AI model settings updated to: {0} - {1}", "model.list.failed": "Failed to get model list", "model.picker.title": "Select AI Model", "model.picker.placeholder": "Select AI model for generating commit messages", "progress.generating.commit": "Generating {0} commit message...", "progress.analyzing.changes": "Analyzing changes...", "progress.generation.complete": "Generation complete", "ollama.models.updated": "Ollama model list updated", "ollama.models.fetch.failed": "Failed to fetch Ollama model list", "ollama.api.call.failed": "Ollama API call failed", "ollama.api.request.failed": "Ollama API request failed: {0}", "localization.manager.not.initialized": "Localization manager not initialized", "openai.config.invalid": "OpenAI configuration invalid", "openai.models.update.success": "OpenAI model list updated", "openai.models.fetch.failed": "Failed to fetch OpenAI model list", "provider.not.available": "AI provider {0} is not available", "provider.type.unknown": "Unknown AI provider type: {0}", "unexpected.error": "Unexpected error occurred: {0}", "vscode.no.models.available": "No VSCode language models available", "vscode.generation.failed": "VSCode AI generation failed: {0}", "vscode.models.fetch.failed": "Failed to fetch VSCode model list", "generation.failed": "{0}", "button.yes": "Yes", "button.no": "No", "workspace.not.found": "Workspace folder not found", "git.diff.error": "Git diff error: {0}", "diff.noChanges": "No changes detected", "git.diff.failed": "Git diff failed: {0}", "git.repository.not.found": "Git repository not found", "diff.noChangesFound": "No changes found", "svn.commit.failed": "SVN commit failed: {0}", "svn.no.changes": "No changes to commit in SVN", "svn.repository.not.found": "SVN repository not found", "openai.input.truncated": "Input content too long and has been truncated, this may affect the quality of generated results", "openai.generation.failed": "OpenAI generation failed: {0}", "ollama.input.truncated": "Input content too long and has been truncated, this may affect the quality of generated results", "ollama.generation.failed": "Ollama generation failed: {0}", "openai.models.empty": "No OpenAI models available", "openai.models.error": "Failed to get OpenAI model list", "model.not.found": "Selected model not found", "model.list.empty": "Model list is empty", "model.list.partial.failed": "Failed to fetch models from some providers: {0}", "no.commit.message.generated": "No commit message generated", "input.truncated": "Input content exceeds maximum character limit and has been truncated, this may affect the quality of generated results", "extension.activation.failed": "Extension activation failed: {0}", "command.register.failed": "Command registration failed: {0}", "command.generate.failed": "Failed to generate commit message: {0}", "command.select.model.failed": "Failed to select model: {0}", "ai.model.loading": "Loading AI model list...", "weeklyReport.generating": "Generating weekly report...", "weeklyReport.empty.response": "AI generated content is empty", "weeklyReport.generation.success": "Weekly report for {1} ({0}) has been generated successfully", "weeklyReport.generation.failed": "Failed to generate weekly report: {0}", "weeklyReport.copy.success": "Content copied to clipboard", "weeklyReport.copy.failed": "Co<PERSON> failed: {0}", "author.svn.not.found": "Unable to get SVN author information", "author.manual.input.prompt": "Unable to automatically get username, please enter manually", "author.manual.input.placeholder": "Please enter username", "weeklyReport.title": "Weekly Report Generator", "weeklyReport.period.current": "This Week", "weeklyReport.period.lastWeek": "Last Week", "weeklyReport.period.twoWeeksAgo": "Two Weeks Ago", "weeklyReport.generate.button": "Generate Report", "editor.format.bold": "Bold", "editor.format.italic": "Italic", "editor.format.underline": "Underline", "editor.format.orderedList": "Ordered List", "editor.format.unorderedList": "Unordered List", "editor.copy": "Copy Content", "zhipu.apiKey.missing": "Zhipu AI API Key is not configured. Would you like to configure it now?", "gemini.apiKey.missing": "Gemini API Key is not configured. Would you like to configure it now?", "dashscope.apiKey.missing": "DashScope API Key is not configured. Would you like to configure it now?", "doubao.apiKey.missing": "Doubao API Key is not configured. Would you like to configure it now?", "reviewing.code": "Reviewing code", "getting.file.changes": "Getting file changes...", "no.valid.changes.selected": "No valid changes selected for review", "reviewing.file": "file: {0}", "preparing.results": "Preparing review results...", "review.all.failed": "All files review failed", "review.complete.count": "Completed code review for {0} files", "review.results.title": "Code Review Results", "codeReview.generation.failed": "Code review generation failed: {0}", "no.changes.selected": "No changes selected! Please select files to analyze", "no.changes.found": "No changes found in selected files", "please.select.valid.files": "Some selected files are invalid, please reselect files for review", "review.file.failed": "Failed to review file: {0}", "code.review.failed": "Code review failed: {0}", "svn.no.files.selected": "No files selected to commit", "scm.no.provider": "No SVN or Git extension found, please install corresponding version control extension", "svn.lastModifiedAuthor": "Last Modified Author:", "svn.authRealm": "Authentication Realm:", "codeReview.report.title": "Code Review Report", "codeReview.report.summary": "Summary", "codeReview.report.findings": "Detailed Findings", "codeReview.issue.label": "Issue:", "codeReview.suggestion.label": "Suggestion:", "codeReview.documentation.label": "Documentation", "cli.commit.input.not.supported": "Unable to access the commit message input box instance", "svn.executable.not.found": "SVN executable not found", "svn.path.detection.failed": "Failed to detect SVN path: {0}", "svn.initialization.failed": "SVN initialization failed: {0}", "svn.version.check.failed": "Failed to check SVN version: {0}", "svn.config.load.failed": "Failed to load SVN configuration: {0}", "svn.invalid.env.config": "Invalid SVN environment configuration", "svn.not.initialized": "SVN provider not initialized", "generating.branch.name": "Generating branch name...", "branch.name.generation.failed": "Branch name generation failed", "branch.name.suggestion": "Suggested branch name: {0}", "branch.name.selected": "You selected branch: {0}", "select.or.edit.branch.name": "Select or edit branch name", "create.branch": "Create Branch", "copy.to.clipboard": "Copy to Clipboard", "branch.created": "Branch created: {0}", "branch.creation.failed": "Branch creation failed", "branch.name.copied": "Branch name copied to clipboard", "branch.name.copy.failed": "Failed to copy branch name", "branch.name.git.only": "Branch name generation is only available for Git repositories", "command.branch.name.failed": "Branch name generation command failed: {0}", "branch.gen.mode.from.changes.label": "Generate from file changes", "branch.gen.mode.from.changes.description": "Analyze the diff of selected files", "branch.gen.mode.from.changes.detail": "Recommended for bug fixes or refactoring", "branch.gen.mode.from.description.label": "Generate from description", "branch.gen.mode.from.description.description": "Enter a description of the feature or issue", "branch.gen.mode.from.description.detail": "Recommended for new features", "branch.gen.mode.select.placeholder": "Select branch name generation mode", "no.model.selected": "No model selected", "weeklyReport.generatingTeamReport": "Generating team weekly report...", "weeklyReport.teamGeneration.success": "Team weekly report for {1} ({0}) has been generated successfully", "weeklyReport.teamGeneration.failed": "Failed to generate team weekly report: {0}", "weeklyReport.getUsers.failed": "Failed to get user list: {0}", "enter.branch.description.prompt": "Please enter a description for the new branch (e.g., feature to implement or issue to fix):", "enter.branch.description.placeholder": "e.g., Add user authentication feature", "branch.description.cancelled": "Branch name generation from description cancelled.", "internal.error.no.ai.input": "Internal error: No input content available for AI processing.", "processing.description": "Processing description...", "analyzing.description": "Analyzing description...", "commit.message.generated.stream": "Commit message generated successfully (streamed by {1} - {2})", "generate.commit.stream.failed": "Failed to generate commit message via stream: {0}", "provider.does.not.support.streaming": "Provider {0} does not support streaming generation.", "confirm.ai.provider.tos.accept": "Accept", "confirm.ai.provider.tos.acceptWorkspace": "Accept Only for this Workspace", "confirm.ai.provider.tos.cancel": "Cancel", "confirm.ai.provider.tos.message": "Dish AI features can send code snippets, diffs, and other context to your selected AI provider for analysis.", "user.cancelled.operation.log": "User cancelled the operation", "user.cancelled.operation.error": "Operation cancelled by user", "pr.summary.git.only": "PR Summary generation is only available for Git repositories", "pr.summary.no.commits": "No commits found to generate PR summary", "progress.generating.pr.summary": "Generating PR summary...", "detecting.scm.provider": "Detecting SCM provider...", "fetching.branches": "Fetching branches...", "fetching.commit.log": "Fetching commit log...", "validating.model": "Validating model...", "analyzing.commits": "Analyzing commits...", "pr.summary.title": "PR Summary", "pr.summary.generated": "PR Summary generated successfully and opened in a new tab", "pr.summary.generation.failed": "Failed to generate PR summary", "pr.summary.generation.failed.error": "Failed to generate PR summary: {0}", "command.pr.summary.failed": "PR Summary generation command failed: {0}", "provider.does.not.support.feature": "Provider {0} does not support {1}.", "svn.log.failed": "Failed to get SVN commit log: {0}", "git.branch.list.failed": "Failed to get Git branch list: {0}", "pr.summary.select.base.branch.placeholder": "Select base branch to compare with (current: {0})", "pr.summary.select.base.branch.title": "Select Base Branch for PR Summary", "pr.summary.base.branch.selection.cancelled": "Base branch selection cancelled. PR summary generation aborted.", "pr.summary.no.branches.found": "No branches found in the repository. Using default base branch.", "progress.failed": "Task execution failed: {0}", "svn.version.detected": "Detected SVN version: {0}", "git.version.detected": "Detected Git version: {0}", "progress.getting.diff": "Getting file diff...", "progress.updating.model.config": "Updating model configuration...", "progress.preparing.request": "Preparing AI request...", "progress.calling.ai.stream": "Calling AI service...", "progress.calling.ai.function": "Calling AI function...", "progress.getting.recent.commits": "Getting recent commits...", "embedding.enabledMessage": "Code similarity search is enabled. Finding related code snippets based on your changes...", "embedding.openai.apiKey.missing": "OpenAI API key is not configured.", "embedding.openai.noEmbeddings": "OpenAI API returned no embeddings.", "embedding.openai.unknownError": "An unknown error occurred with OpenAI", "embedding.ollama.baseUrl.missing": "Ollama API base URL is not configured.", "embedding.ollama.requestFailed": "Ollama API request failed with status {0}", "embedding.ollama.invalidFormat": "Invalid embedding format received from Ollama API.", "embedding.file.indexingComplete": "File {0} indexing complete!", "embedding.save.failed": "Failed to save embeddings to vector store.", "embedding.indexing.unknownError": "An unknown error occurred during indexing.", "embedding.delete.failed": "Failed to delete index from vector store.", "embedding.clear.failed": "Failed to clear index from vector store.", "embedding.provider.apiKey.missing": "{0} API Key is not configured. Skipping embedding generation.", "embedding.provider.apiKey.notConfigured": "{0} API Key not configured.", "embedding.ollama.baseUrl.missing.forSearch": "Ollama base URL is not configured. Skipping search.", "embedding.provider.apiKey.missing.forSearch": "{0} API key is not configured. Skipping search.", "embedding.query.failed": "Failed to generate query embedding.", "embedding.search.failed": "Failed to search in vector store.", "embedding.vectorStore.connectFailed": "Failed to connect to vector store or invalid response.", "embedding.qdrant.connectFailed": "Failed to connect to Qdrant vector database. Please confirm that the Qdrant service is running and accessible via {0}.\\nOriginal error: {1}", "embedding.vectorStore.statusCheck.unknownError": "Unable to check vector index status (unknown error)", "info.using.function.calling": "Generating commit message using function calling mode...", "prompt.length.info": "Prompt length: {0} characters. Model max limit: {1} characters.", "context.truncated": "Context block '{0}' was truncated to fit within the model's token limit.", "context.block.truncated": "Context block '{0}' was partially truncated to reduce context size.", "context.block.removed": "Context block '{0}' was removed to reduce context size.", "prompt.large.warning": "The estimated prompt size ({0} tokens) is close to the model's limit ({1} tokens). This may lead to truncation or failure.", "prompt.large.continue": "Continue Anyway", "prompt.large.cancel": "Cancel", "prompt.user.cancelled": "Operation cancelled by user due to large prompt size.", "error.request.too.large": "The request is too large to be processed by the model. Please reduce the number of selected files or simplify the changes.", "error.switch.to.larger.model": "Switch to a Larger Model", "git.base.branch.not.found": "Base branch '{0}' not found locally or on remote 'origin'.", "git.base.branch.not.found.default": "Default base branch '{0}' not found. Please check your repository configuration.", "git.base.branch.not.found.error": "Could not find a valid base branch to compare against. Tried: {0}", "git.log.failed": "Failed to get Git commit log: {0}", "diff.files.selected": "Selected {0} files for diff.", "diff.staged.info": "Getting staged changes for {0} files.", "diff.all.info": "Getting all changes for {0} files.", "checking.selected.files": "Checking selected files...", "progress.generating.layered.commit": "Generating layered commit details...", "progress.generating.commit.for.file": "Generating details for {0}...", "provider.does.not.support.non.streaming.for.layered": "Provider {0} does not support non-streaming generation required for layered commits.", "layered.commit.details.title": "Layered Commit Details", "layered.commit.details.generated": "Layered commit details have been generated and opened in a new tab."}