{
  "$schema": "https://json.schemastore.org/jsconfig",
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2022",
    "outDir": "out",
    "lib": ["ES2022", "DOM"],
    "sourceMap": false,
    "rootDir": "src",
    "skipLibCheck": true,
    "strict": true /* enable all strict type-checking options */,
    /* Additional Checks */
    // "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
    // "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
    // "noUnusedParameters": true,  /* Report errors on unused parameters. */
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "paths": {
      "@/*": ["./src/webview-ui/src/*"]
    },
    "jsx": "react-jsx"
  },
  "exclude": ["node_modules", "esbuild.ts", "vitest.config.ts"]
}
