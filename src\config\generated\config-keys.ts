// This file is auto-generated, do not edit manually
export const CONFIG_KEYS = {
  "BASE": "dish-ai-commit.base",
  "BASE_LANGUAGE": "dish-ai-commit.base.language",
  "BASE_PROVIDER": "dish-ai-commit.base.provider",
  "BASE_MODEL": "dish-ai-commit.base.model",
  "PROVIDERS": "dish-ai-commit.providers",
  "PROVIDERS_OPENAI": "dish-ai-commit.providers.openai",
  "PROVIDERS_OPENAI_APIKEY": "dish-ai-commit.providers.openai.apiKey",
  "PROVIDERS_OPENAI_BASEURL": "dish-ai-commit.providers.openai.baseUrl",
  "PROVIDERS_ZHIPU": "dish-ai-commit.providers.zhipu",
  "PROVIDERS_ZHIPU_APIKEY": "dish-ai-commit.providers.zhipu.apiKey",
  "PROVIDERS_DASHSCOPE": "dish-ai-commit.providers.dashscope",
  "PROVIDERS_DASHSCOPE_APIKEY": "dish-ai-commit.providers.dashscope.apiKey",
  "PROVIDERS_DOUBAO": "dish-ai-commit.providers.doubao",
  "PROVIDERS_DOUBAO_APIKEY": "dish-ai-commit.providers.doubao.apiKey",
  "PROVIDERS_OLLAMA": "dish-ai-commit.providers.ollama",
  "PROVIDERS_OLLAMA_BASEURL": "dish-ai-commit.providers.ollama.baseUrl",
  "PROVIDERS_GEMINI": "dish-ai-commit.providers.gemini",
  "PROVIDERS_GEMINI_APIKEY": "dish-ai-commit.providers.gemini.apiKey",
  "PROVIDERS_BAIDUQIANFAN": "dish-ai-commit.providers.baiduQianfan",
  "PROVIDERS_BAIDUQIANFAN_APIKEY": "dish-ai-commit.providers.baiduQianfan.apiKey",
  "PROVIDERS_BAIDUQIANFAN_SECRETKEY": "dish-ai-commit.providers.baiduQianfan.secretKey",
  "PROVIDERS_DEEPSEEK": "dish-ai-commit.providers.deepseek",
  "PROVIDERS_DEEPSEEK_APIKEY": "dish-ai-commit.providers.deepseek.apiKey",
  "PROVIDERS_SILICONFLOW": "dish-ai-commit.providers.siliconflow",
  "PROVIDERS_SILICONFLOW_APIKEY": "dish-ai-commit.providers.siliconflow.apiKey",
  "PROVIDERS_OPENROUTER": "dish-ai-commit.providers.openrouter",
  "PROVIDERS_OPENROUTER_APIKEY": "dish-ai-commit.providers.openrouter.apiKey",
  "PROVIDERS_PERPLEXITY": "dish-ai-commit.providers.perplexity",
  "PROVIDERS_PERPLEXITY_APIKEY": "dish-ai-commit.providers.perplexity.apiKey",
  "PROVIDERS_PREMAI": "dish-ai-commit.providers.premai",
  "PROVIDERS_PREMAI_APIKEY": "dish-ai-commit.providers.premai.apiKey",
  "PROVIDERS_PREMAI_BASEURL": "dish-ai-commit.providers.premai.baseUrl",
  "PROVIDERS_TOGETHER": "dish-ai-commit.providers.together",
  "PROVIDERS_TOGETHER_APIKEY": "dish-ai-commit.providers.together.apiKey",
  "PROVIDERS_TOGETHER_BASEURL": "dish-ai-commit.providers.together.baseUrl",
  "PROVIDERS_XAI": "dish-ai-commit.providers.xai",
  "PROVIDERS_XAI_APIKEY": "dish-ai-commit.providers.xai.apiKey",
  "PROVIDERS_ANTHROPIC": "dish-ai-commit.providers.anthropic",
  "PROVIDERS_ANTHROPIC_APIKEY": "dish-ai-commit.providers.anthropic.apiKey",
  "PROVIDERS_MISTRAL": "dish-ai-commit.providers.mistral",
  "PROVIDERS_MISTRAL_APIKEY": "dish-ai-commit.providers.mistral.apiKey",
  "PROVIDERS_AZUREOPENAI": "dish-ai-commit.providers.azureOpenai",
  "PROVIDERS_AZUREOPENAI_APIKEY": "dish-ai-commit.providers.azureOpenai.apiKey",
  "PROVIDERS_AZUREOPENAI_ENDPOINT": "dish-ai-commit.providers.azureOpenai.endpoint",
  "PROVIDERS_AZUREOPENAI_APIVERSION": "dish-ai-commit.providers.azureOpenai.apiVersion",
  "PROVIDERS_AZUREOPENAI_ORGID": "dish-ai-commit.providers.azureOpenai.orgId",
  "PROVIDERS_CLOUDFLARE": "dish-ai-commit.providers.cloudflare",
  "PROVIDERS_CLOUDFLARE_APIKEY": "dish-ai-commit.providers.cloudflare.apiKey",
  "PROVIDERS_CLOUDFLARE_ACCOUNTID": "dish-ai-commit.providers.cloudflare.accountId",
  "PROVIDERS_VERTEXAI": "dish-ai-commit.providers.vertexai",
  "PROVIDERS_VERTEXAI_PROJECTID": "dish-ai-commit.providers.vertexai.projectId",
  "PROVIDERS_VERTEXAI_LOCATION": "dish-ai-commit.providers.vertexai.location",
  "PROVIDERS_VERTEXAI_APIENDPOINT": "dish-ai-commit.providers.vertexai.apiEndpoint",
  "PROVIDERS_VERTEXAI_GOOGLEAUTHOPTIONS": "dish-ai-commit.providers.vertexai.googleAuthOptions",
  "PROVIDERS_GROQ": "dish-ai-commit.providers.groq",
  "PROVIDERS_GROQ_APIKEY": "dish-ai-commit.providers.groq.apiKey",
  "FEATURES": "dish-ai-commit.features",
  "FEATURES_CODEANALYSIS": "dish-ai-commit.features.codeAnalysis",
  "FEATURES_CODEANALYSIS_DIFFTARGET": "dish-ai-commit.features.codeAnalysis.diffTarget",
  "FEATURES_CODEANALYSIS_SIMPLIFYDIFF": "dish-ai-commit.features.codeAnalysis.simplifyDiff",
  "FEATURES_COMMITFORMAT": "dish-ai-commit.features.commitFormat",
  "FEATURES_COMMITFORMAT_ENABLEEMOJI": "dish-ai-commit.features.commitFormat.enableEmoji",
  "FEATURES_COMMITFORMAT_ENABLEMERGECOMMIT": "dish-ai-commit.features.commitFormat.enableMergeCommit",
  "FEATURES_COMMITFORMAT_ENABLEBODY": "dish-ai-commit.features.commitFormat.enableBody",
  "FEATURES_COMMITFORMAT_ENABLELAYEREDCOMMIT": "dish-ai-commit.features.commitFormat.enableLayeredCommit",
  "FEATURES_COMMITMESSAGE": "dish-ai-commit.features.commitMessage",
  "FEATURES_COMMITMESSAGE_SYSTEMPROMPT": "dish-ai-commit.features.commitMessage.systemPrompt",
  "FEATURES_COMMITMESSAGE_USERECENTCOMMITSASREFERENCE": "dish-ai-commit.features.commitMessage.useRecentCommitsAsReference",
  "FEATURES_WEEKLYREPORT": "dish-ai-commit.features.weeklyReport",
  "FEATURES_WEEKLYREPORT_SYSTEMPROMPT": "dish-ai-commit.features.weeklyReport.systemPrompt",
  "FEATURES_CODEREVIEW": "dish-ai-commit.features.codeReview",
  "FEATURES_CODEREVIEW_SYSTEMPROMPT": "dish-ai-commit.features.codeReview.systemPrompt",
  "FEATURES_BRANCHNAME": "dish-ai-commit.features.branchName",
  "FEATURES_BRANCHNAME_SYSTEMPROMPT": "dish-ai-commit.features.branchName.systemPrompt",
  "FEATURES_PRSUMMARY": "dish-ai-commit.features.prSummary",
  "FEATURES_PRSUMMARY_BASEBRANCH": "dish-ai-commit.features.prSummary.baseBranch",
  "FEATURES_PRSUMMARY_HEADBRANCH": "dish-ai-commit.features.prSummary.headBranch",
  "FEATURES_PRSUMMARY_SYSTEMPROMPT": "dish-ai-commit.features.prSummary.systemPrompt",
  "FEATURES_PRSUMMARY_COMMITLOGLIMIT": "dish-ai-commit.features.prSummary.commitLogLimit"
} as const;
