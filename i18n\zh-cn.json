{"openai.apiKey.missing": "OpenAI API Key 未配置。是否现在配置？", "ollama.baseUrl.missing": "Ollama Base URL 未配置。是否现在配置？", "diff.simplification.warning": "Diff 简化功能已启用。这可能会影响 AI 生成提交信息的准确性。如需获取更准确的提交信息，建议禁用此功能。", "command.execution.failed": "执行命令失败", "scm.not.detected": "未检测到支持的版本控制系统", "no.changes": "没有可提交的更改", "diff.too.long": "变更内容过长，超出模型最大限制。请减少选中的文件数量或内容长度。\n当前长度: {0} 字符\n最大限制: {1} 字符", "commit.message.generated": "提交信息生成成功 (生成自 {1} - {2})", "commit.message.write.failed": "写入提交信息到版本控制输入框失败: {0}", "commit.message.copied": "提交信息已复制到剪贴板，请粘贴到版本控制系统的提交输入框中", "commit.message.copy.failed": "复制提交信息失败: {0}", "commit.message.manual.copy": "提交信息已生成，请手动复制到提交框: \n {0}", "generate.commit.failed": "生成提交信息失败: {0}", "get.models.failed": "获取模型列表失败", "openai.config.required": "需要配置 OpenAI API 信息才能使用该功能，是否现在配置？", "openai.baseUrl.prompt": "请输入 OpenAI API 地址", "openai.baseUrl.placeholder": "例如: https://api.openai.com/v1", "openai.apiKey.prompt": "请输入 OpenAI API Key", "ai.model.picker.title": "选择 AI 模型", "ai.model.picker.placeholder": "选择用于生成提交信息的 AI 模型", "model.update.success": "已更新 AI 模型设置为: {0} - {1}", "model.list.failed": "获取模型列表失败", "model.picker.title": "选择 AI 模型", "model.picker.placeholder": "选择用于生成提交信息的 AI 模型", "progress.generating.commit": "正在生成 {0} 提交信息...", "progress.analyzing.changes": "正在分析变更内容...", "progress.generation.complete": "生成完成", "ollama.models.updated": "Ollama模型列表已更新", "ollama.models.fetch.failed": "获取Ollama模型列表失败", "ollama.api.call.failed": "Ollama API调用失败", "ollama.api.request.failed": "Ollama API请求失败: {0}", "localization.manager.not.initialized": "本地化管理器未初始化", "openai.config.invalid": "OpenAI 配置无效", "openai.models.update.success": "OpenAI模型列表已更新", "openai.models.fetch.failed": "获取OpenAI模型列表失败", "provider.not.available": "AI 提供商 {0} 不可用", "provider.type.unknown": "未知的 AI 提供商类型：{0}", "unexpected.error": "发生意外错误：{0}", "vscode.no.models.available": "未找到可用的VSCode语言模型", "vscode.generation.failed": "VSCode AI生成失败: {0}", "vscode.models.fetch.failed": "获取VSCode模型列表失败", "generation.failed": "{0}", "button.yes": "是", "button.no": "否", "workspace.not.found": "未找到工作区文件夹", "git.diff.error": "Git差异比较错误: {0}", "diff.noChanges": "没有发现任何更改", "git.diff.failed": "Git差异比较失败: {0}", "git.repository.not.found": "未找到Git仓库", "diff.noChangesFound": "未发现任何更改", "svn.commit.failed": "SVN提交失败: {0}", "svn.no.changes": "SVN没有要提交的更改", "svn.repository.not.found": "未找到SVN仓库", "openai.input.truncated": "输入内容过长已被截断，这可能会影响生成结果的质量", "openai.generation.failed": "OpenAI生成失败: {0}", "ollama.input.truncated": "输入内容过长已被截断，这可能会影响生成结果的质量", "ollama.generation.failed": "<PERSON><PERSON><PERSON>生成失败: {0}", "openai.models.empty": "未找到可用的OpenAI模型", "openai.models.error": "获取OpenAI模型列表失败", "model.not.found": "未找到选定的模型", "no.commit.message.generated": "未生成提交信息", "input.truncated": "输入内容超过最大字符数限制，已被截断，这可能会影响生成结果的质量", "extension.activation.failed": "激活扩展失败: {0}", "command.register.failed": "注册命令失败: {0}", "command.generate.failed": "生成提交信息失败: {0}", "command.select.model.failed": "选择模型失败: {0}", "ai.model.loading": "正在加载 AI 模型列表...", "weeklyReport.generating": "正在生成周报...", "weeklyReport.empty.response": "AI 生成内容为空", "weeklyReport.generation.success": "{1}的{0}工作周报已生成完成", "weeklyReport.generation.failed": "生成周报失败: {0}", "weeklyReport.copy.success": "内容已复制到剪贴板", "weeklyReport.copy.failed": "复制失败: {0}", "author.svn.not.found": "无法获取 SVN 作者信息", "author.manual.input.prompt": "无法自动获取用户名，请手动输入", "author.manual.input.placeholder": "请输入用户名", "weeklyReport.title": "周报生成器", "weeklyReport.period.current": "本周", "weeklyReport.period.lastWeek": "上一周", "weeklyReport.period.twoWeeksAgo": "上两周", "weeklyReport.generate.button": "生成周报", "editor.format.bold": "粗体", "editor.format.italic": "斜体", "editor.format.underline": "下划线", "editor.format.orderedList": "有序列表", "editor.format.unorderedList": "无序列表", "editor.copy": "复制内容", "zhipu.apiKey.missing": "智谱 AI API Key 未配置。是否现在配置？", "gemini.apiKey.missing": "Gemini API Key 未配置。是否现在配置？", "dashscope.apiKey.missing": "DashScope API Key 未配置。是否现在配置？", "doubao.apiKey.missing": "豆包 API Key 未配置。是否现在配置？", "reviewing.code": "正在进行代码评审", "no.valid.changes.selected": "未选择有效的待评审变更", "reviewing.file": "评审文件：{0}", "review.all.failed": "所有文件评审失败", "review.complete.count": "已完成 {0} 个文件的代码评审", "review.results.title": "代码评审结果", "codeReview.generation.failed": "代码评审生成失败: {0}", "model.list.empty": "模型列表为空", "model.list.partial.failed": "部分 AI 提供商模型列表获取失败: {0}", "no.changes.selected": "没有选择任何更改！请先选择需要分析的文件", "no.changes.found": "在所选文件中未找到任何更改", "getting.file.changes": "正在获取文件更改...", "analyzing.code.changes": "正在分析代码更改...", "preparing.results": "正在准备结果...", "generating.branch.name": "正在生成分支名称...", "branch.name.generation.failed": "分支名称生成失败", "branch.name.suggestion": "建议的分支名称: {0}", "branch.name.suggestions": "分支名称建议", "branch.name.selected": "您已选择分支：{0}", "select.or.edit.branch.name": "选择或编辑分支名称", "create.branch": "创建分支", "copy.to.clipboard": "复制到剪贴板", "branch.created": "已创建分支: {0}", "branch.creation.failed": "分支创建失败", "branch.name.copied": "已复制分支名称到剪贴板", "branch.name.copy.failed": "复制分支名称失败", "branch.name.git.only": "分支名称生成功能仅适用于Git仓库", "command.branch.name.failed": "分支名称生成命令执行失败：{0}", "branch.gen.mode.from.changes.label": "从文件变更生成", "branch.gen.mode.from.changes.description": "分析所选文件的差异", "branch.gen.mode.from.changes.detail": "建议用于修复 Bug 或重构", "branch.gen.mode.from.description.label": "从描述生成", "branch.gen.mode.from.description.description": "输入功能或问题的描述", "branch.gen.mode.from.description.detail": "建议用于新功能开发", "branch.gen.mode.select.placeholder": "选择分支名称生成模式", "please.select.valid.files": "选择的文件中包含无效项，请重新选择要评审的文件", "review.file.failed": "评审文件失败: {0}", "code.review.failed": "代码评审失败: {0}", "svn.no.files.selected": "未选择要提交的文件", "scm.no.provider": "未找到可用的SVN或Git扩展，请安装对应的版本控制扩展", "svn.lastModifiedAuthor": "最后修改的作者:", "svn.authRealm": "认证领域:", "codeReview.report.title": "代码评审报告", "codeReview.report.summary": "总体摘要", "codeReview.report.findings": "详细问题", "codeReview.issue.label": "问题：", "codeReview.suggestion.label": "建议：", "codeReview.documentation.label": "相关文档", "cli.commit.input.not.supported": "无法获取提交信息输入框实例", "svn.executable.not.found": "无法找到SVN可执行文件", "svn.path.detection.failed": "检测SVN路径失败: {0}", "svn.initialization.failed": "SVN初始化失败: {0}", "svn.version.check.failed": "检查SVN版本失败: {0}", "svn.config.load.failed": "加载SVN配置失败: {0}", "svn.invalid.env.config": "无效的SVN环境配置", "svn.not.initialized": "SVN提供程序未初始化", "no.model.selected": "未选择模型", "weeklyReport.generatingTeamReport": "正在生成团队周报...", "weeklyReport.teamGeneration.success": "团队成员 {1} 的 {0} 工作周报已生成完成", "weeklyReport.teamGeneration.failed": "生成团队周报失败: {0}", "weeklyReport.getUsers.failed": "获取用户列表失败: {0}", "enter.branch.description.prompt": "请输入新分支的描述（例如，要实现的功能或要修复的问题）：", "enter.branch.description.placeholder": "例如：添加用户认证功能", "branch.description.cancelled": "已取消通过描述生成分支名称。", "internal.error.no.ai.input": "内部错误：没有可用于AI处理的输入内容。", "processing.description": "正在处理描述...", "analyzing.description": "正在分析描述...", "commit.message.generated.stream": "提交信息已通过流式方式成功生成 (由 {1} - {2} 生成)", "generate.commit.stream.failed": "通过流式方式生成提交信息失败: {0}", "provider.does.not.support.streaming": "提供商 {0} 不支持流式生成。", "confirm.ai.provider.tos.accept": "接受", "confirm.ai.provider.tos.acceptWorkspace": "仅针对此工作区接受", "confirm.ai.provider.tos.cancel": "取消", "confirm.ai.provider.tos.message": "Dish AI 功能可以将代码片段、差异和其他上下文发送到您选择的 AI 提供商进行分析。", "user.cancelled.operation.log": "用户取消了操作", "user.cancelled.operation.error": "操作已被用户取消", "pr.summary.git.only": "PR摘要生成功能仅适用于Git仓库", "pr.summary.no.commits": "未找到可用于生成PR摘要的提交记录", "progress.generating.pr.summary": "正在生成PR摘要...", "detecting.scm.provider": "正在检测SCM提供商...", "fetching.branches": "正在获取分支列表...", "fetching.commit.log": "正在获取提交日志...", "validating.model": "正在验证模型...", "analyzing.commits": "正在分析提交记录...", "pr.summary.title": "PR摘要", "pr.summary.generated": "PR摘要已成功生成并在新标签页中打开", "pr.summary.generation.failed": "生成PR摘要失败", "pr.summary.generation.failed.error": "生成PR摘要失败: {0}", "command.pr.summary.failed": "PR摘要生成命令执行失败: {0}", "provider.does.not.support.feature": "提供商 {0} 不支持 {1} 功能。", "svn.log.failed": "获取 SVN 提交日志失败: {0}", "git.branch.list.failed": "获取 Git 分支列表失败: {0}", "pr.summary.select.base.branch.placeholder": "选择要对比的基础分支 (当前: {0})", "pr.summary.select.base.branch.title": "选择PR摘要的基础分支", "pr.summary.base.branch.selection.cancelled": "已取消选择基础分支。PR摘要生成已中止。", "pr.summary.no.branches.found": "仓库中未找到分支。将使用默认基础分支。", "progress.failed": "任务执行失败：{0}", "svn.version.detected": "检测到 SVN 版本: {0}", "git.version.detected": "检测到 Git 版本: {0}", "progress.getting.diff": "正在获取文件差异...", "progress.updating.model.config": "正在更新模型配置...", "progress.preparing.request": "正在准备AI请求...", "progress.calling.ai.stream": "正在调用AI服务...", "progress.calling.ai.function": "正在调用AI函数...", "progress.getting.recent.commits": "正在获取最近的提交记录...", "embedding.enabledMessage": "代码相似度搜索功能已启用。正在基于您的变更寻找相关代码片段...", "embedding.openai.apiKey.missing": "OpenAI API 密钥未配置。", "embedding.openai.noEmbeddings": "OpenAI API 未返回任何嵌入。", "embedding.openai.unknownError": "OpenAI 发生未知错误", "embedding.ollama.baseUrl.missing": "Ollama API 基础 URL 未配置。", "embedding.ollama.requestFailed": "Ollama API 请求失败，状态码：{0}", "embedding.ollama.invalidFormat": "从 Ollama API 收到无效的嵌入格式。", "embedding.file.indexingComplete": "文件 {0} 索引完成！", "embedding.save.failed": "无法将嵌入保存到向量存储。", "embedding.indexing.unknownError": "索引过程中发生未知错误。", "embedding.delete.failed": "无法从向量存储中删除索引。", "embedding.clear.failed": "无法从向量存储中清除索引。", "embedding.provider.apiKey.missing": "{0} API 密钥未配置。跳过嵌入生成。", "embedding.provider.apiKey.notConfigured": "{0} API 密钥未配置。", "embedding.ollama.baseUrl.missing.forSearch": "Ollama 基础 URL 未配置。跳过搜索。", "embedding.provider.apiKey.missing.forSearch": "{0} API 密钥未配置。跳过搜索。", "embedding.query.failed": "无法生成查询嵌入。", "embedding.search.failed": "无法在向量存储中搜索。", "embedding.vectorStore.connectFailed": "无法连接到向量存储或响应无效。", "embedding.qdrant.connectFailed": "连接 Qdrant 向量数据库失败。请确认 Qdrant 服务正在运行，并确保可以通过 {0} 访问。\n原始错误：{1}", "embedding.vectorStore.statusCheck.unknownError": "无法检查向量索引状态（未知错误）", "info.using.function.calling": "正在使用函数调用模式生成提交信息...", "prompt.length.info": "提示词长度: {0} 字符。模型最大限制: {1} 字符。", "context.truncated": "上下文区块 '{0}' 已被截断以适应模型的令牌限制。", "context.block.truncated": "为缩减上下文大小，区块 '{0}' 的部分内容已被截断。", "context.block.removed": "为缩减上下文大小，区块 '{0}' 已被移除。", "prompt.large.warning": "预估的提示词大小（{0} tokens）已接近模型上限（{1} tokens），这可能导致内容被截断或生成失败。", "prompt.large.continue": "仍然继续", "prompt.large.cancel": "取消", "prompt.user.cancelled": "因提示词过大，用户已取消操作。", "error.request.too.large": "请求内容过大，无法被模型处理。请减少选择的文件数量或简化变更内容。", "error.switch.to.larger.model": "切换到更大模型", "git.base.branch.not.found": "在本地或远程 'origin' 未找到基础分支 '{0}'。", "git.base.branch.not.found.default": "未找到默认基础分支 '{0}'。请检查您的仓库配置。", "git.base.branch.not.found.error": "无法找到有效的基础分支进行比较。已尝试: {0}", "git.log.failed": "获取 Git 提交日志失败: {0}", "diff.files.selected": "已选择 {0} 个文件进行比较。", "diff.staged.info": "正在获取 {0} 个文件的暂存更改。", "diff.all.info": "正在获取所有 {0} 个文件的更改。", "checking.selected.files": "正在检查选定的文件...", "progress.generating.layered.commit": "正在生成分层提交详情...", "progress.generating.commit.for.file": "正在为 {0} 生成详情...", "provider.does.not.support.non.streaming.for.layered": "提供商 {0} 不支持分层提交所需的非流式生成。", "layered.commit.details.title": "分层提交详情", "layered.commit.details.generated": "分层提交详情已生成并在新标签页中打开。"}