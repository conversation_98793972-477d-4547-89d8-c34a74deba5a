{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "extensionHost",
      "request": "launch",
      "name": "启动扩展",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "args": [
        // "--disable-extensions",
        "--enable-extension=littleCareless.svn-scm-ai",
        "--enable-extension=vscode.git",
        "--enable-extension=GitHub.copilot",
        "--enable-extension=GitHub.copilot-chat",
        "--extensionDevelopmentPath=${workspaceFolder}"
      ],
      "preLaunchTask": "${defaultBuildTask}"
    }
  ]
}
