on:
  push:
    branches:
      - main
      - develop
    tags:
      - "v*.*.*"
  workflow_dispatch:

name: Deploy Extension
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Install pnpm
        run: npm install -g pnpm
      - run: npm ci
      - name: Publish to Open VSX Registry
        uses: HaaLeo/publish-vscode-extension@v2
        id: publishToOpenVSX
        with:
          pat: ${{ secrets.OPEN_VSX_TOKEN }}
          preRelease: ${{ github.ref == 'refs/heads/develop' }}
          skipDuplicate: true
      - name: Publish to Visual Studio Marketplace
        uses: HaaLeo/publish-vscode-extension@v2
        with:
          pat: ${{ secrets.VS_MARKETPLACE_TOKEN }}
          registryUrl: https://marketplace.visualstudio.com
          extensionFile: ${{ steps.publishToOpenVSX.outputs.vsixPath }}
          skipDuplicate: true
