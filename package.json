{"name": "dish-ai-commit", "displayName": "Dish AI Commit Message Gen", "description": "🤖 由 AI 提供支持的 VSCode 扩展，用于生成标准化的 Git/SVN 提交消息 - 🤖 AI-Powered VSCode extension for generating standardized Git/SVN commit messages. ✨ Supports multiple AI services: OpenAI, ChatGPT, Ollama, Zhipu, DashScope, Doubao, Gemini and VS Code built-in AI. 🌍 Multi-language support (EN/CN/JP/KR/Other). 📊 Auto-generate weekly reports.", "version": "0.35.0", "engines": {"node": ">= 18.20.8", "pnpm": ">= 10.0.0", "vscode": "^1.90.0"}, "publisher": "littleCareless", "repository": "https://github.com/littleCareless/dish-ai-commit", "icon": "images/logo.png", "categories": ["SCM Providers", "Machine Learning", "Snippets", "Formatters", "AI", "Other"], "keywords": ["OpenAI", "ChatGPT", "Ollama", "Vscode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SvnEmoji", "Git Commit", "Svn Commit", "Conventional Commits", "Commitizen", "Commit Message", "Commit Message Generator", "AI Commit", "Auto Commit", "SCM Providers", "SCM", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "DashScope", "Do<PERSON><PERSON>", "Gemini", "Deepseek", "siliconflow"], "extensionDependencies": ["vscode.git"], "main": "./dist/extension.js", "activationEvents": ["onStartupFinished", "onCommand:dish-ai-commit", "onView:dish-ai-commit.settingsView"], "contributes": {"viewsContainers": {"activitybar": [{"id": "dish-ai-commitActivityBar", "title": "Dish Ai Commit Message", "icon": "/images/menu-icon.svg"}]}, "views": {"dish-ai-commitActivityBar": [{"id": "dish-ai-commit.settingsView", "name": "插件设置", "type": "webview", "contextualTitle": "Dish Ai Commit Message设置", "icon": "/images/menu-icon.svg"}]}, "commands": [{"command": "dish-ai-commit.generateCommitMessage", "title": "[Dish] Generate Commit with AI", "category": "[Dish AI Commit]", "icon": {"light": "images/icon.svg", "dark": "images/icon.svg"}, "description": "使用 AI 生成提交信息"}, {"command": "dish-ai-commit.selectModel", "title": "[Dish] Select AI Model", "category": "[Dish AI Commit]", "icon": {"light": "images/light/brain.svg", "dark": "images/dark/brain.svg"}, "description": "选择用于生成提交信息的AI模型"}, {"command": "dish-ai-commit.generateWeeklyReport", "title": "[Dish] Generate Weekly Report with AI", "category": "[Dish AI Commit]", "icon": {"light": "images/light/chart-bar.svg", "dark": "images/dark/chart-bar.svg"}, "description": "使用 AI 生成周报"}, {"command": "dish-ai-commit.reviewCode", "title": "[Dish] Review Code with AI", "category": "[Dish AI Commit]", "icon": {"light": "images/light/clipboard-check.svg", "dark": "images/dark/clipboard-check.svg"}, "description": "使用 AI 对代码进行评审"}, {"command": "dish-ai-commit.generateBranchName", "title": "[Dish] Generate Branch Name with AI", "category": "[Dish AI Commit]", "icon": {"light": "images/light/git-branch.svg", "dark": "images/dark/git-branch.svg"}, "description": "使用 AI 生成Git分支名称"}, {"command": "dish-ai-commit.generatePRSummary", "title": "[Dish] Generate PR Summary with AI", "category": "[Dish AI Commit]", "icon": {"light": "images/light/git-pull-request.svg", "dark": "images/dark/git-pull-request.svg"}, "description": "使用 AI 生成 PR 摘要"}], "configuration": {"title": "Dish AI Commit", "properties": {"dish-ai-commit.base.language": {"type": "string", "default": "Simplified Chinese", "description": "Commit message language / 提交信息语言", "enum": ["Simplified Chinese", "Traditional Chinese", "Japanese", "Korean", "Czech", "German", "French", "Italian", "Dutch", "Portuguese", "Vietnamese", "English", "Spanish", "Swedish", "Russian", "Bahasa", "Polish", "Turkish", "Thai"], "enumDescriptions": ["简体中文", "繁體中文", "にほんご", "한국어", "česky", "De<PERSON>ch", "française", "italiano", "Nederlands", "português", "tiếng <PERSON>", "english", "español", "Svenska", "русский", "bahasa", "<PERSON><PERSON>", "Turkish", "ไทย"]}, "dish-ai-commit.base.provider": {"type": "string", "default": "OpenAI", "description": "AI provider / AI 提供商", "enum": ["OpenAI", "Ollama", "VS Code Provided", "<PERSON><PERSON><PERSON>", "DashScope", "Do<PERSON><PERSON>", "Gemini", "Deepseek", "Siliconflow", "OpenRouter", "PremAI", "Together", "Anthropic", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Azure OpenAI", "Cloudflare", "GoogleAI", "VertexAI"]}, "dish-ai-commit.base.model": {"type": "string", "default": "gpt-3.5-turbo", "description": "AI model / AI 模型", "scope": "machine"}, "dish-ai-commit.providers.openai.apiKey": {"type": "string", "default": "", "description": "OpenAI API Key / OpenAI API 密钥"}, "dish-ai-commit.providers.openai.baseUrl": {"type": "string", "default": "https://api.openai.com/v1", "description": "OpenAI API Base URL / OpenAI API 基础地址"}, "dish-ai-commit.providers.zhipu.apiKey": {"type": "string", "default": "", "description": "Zhipu AI API Key / 智谱 AI API 密钥"}, "dish-ai-commit.providers.dashscope.apiKey": {"type": "string", "default": "", "description": "DashScope API Key / 灵积 API 密钥"}, "dish-ai-commit.providers.doubao.apiKey": {"type": "string", "default": "", "description": "Doubao API Key / 豆包 API 密钥"}, "dish-ai-commit.providers.ollama.baseUrl": {"type": "string", "default": "http://localhost:11434", "description": "Ollama API Base URL / Ollama API 基础地址"}, "dish-ai-commit.providers.gemini.apiKey": {"type": "string", "default": "", "description": "Gemini AI API Key / Gemini AI API 密钥"}, "dish-ai-commit.providers.baiduQianfan.apiKey": {"type": "string", "default": "", "description": "Baidu Qianfan API Key / 百度千帆 API 密钥"}, "dish-ai-commit.providers.baiduQianfan.secretKey": {"type": "string", "default": "", "description": "<PERSON><PERSON> Secret Key / 百度千帆 Secret Key"}, "dish-ai-commit.providers.deepseek.apiKey": {"type": "string", "default": "", "description": "Deepseek AI API Key / Deepseek AI API 密钥"}, "dish-ai-commit.providers.siliconflow.apiKey": {"type": "string", "default": "", "description": "SiliconFlow AI API Key / SiliconFlow AI API 密钥"}, "dish-ai-commit.providers.openrouter.apiKey": {"type": "string", "default": "", "description": "OpenRouter AI API Key / OpenRouter AI API 密钥"}, "dish-ai-commit.providers.perplexity.apiKey": {"type": "string", "default": "", "description": "Perplexity AI API Key / Perplexity AI API 密钥"}, "dish-ai-commit.providers.premai.apiKey": {"type": "string", "default": "", "description": "PremAI API Key / PremAI API 密钥"}, "dish-ai-commit.providers.premai.baseUrl": {"type": "string", "default": "https://api.premai.com/", "description": "PremAI API Base URL / PremAI API 基础地址"}, "dish-ai-commit.providers.together.apiKey": {"type": "string", "default": "", "description": "Together AI API Key / Together AI API 密钥"}, "dish-ai-commit.providers.together.baseUrl": {"type": "string", "default": "https://api.together.xyz/", "description": "Together AI API Base URL / Together AI API 基础地址"}, "dish-ai-commit.providers.xai.apiKey": {"type": "string", "default": "", "description": "xAI API Key / xAI API 密钥"}, "dish-ai-commit.providers.anthropic.apiKey": {"type": "string", "default": "", "description": "Anthropic API Key / Anthropic API 密钥"}, "dish-ai-commit.providers.mistral.apiKey": {"type": "string", "default": "", "description": "Mistral AI API Key / Mistral AI API 密钥"}, "dish-ai-commit.providers.azureOpenai.apiKey": {"type": "string", "default": "", "description": "Azure OpenAI API Key / Azure OpenAI API 密钥"}, "dish-ai-commit.providers.azureOpenai.endpoint": {"type": "string", "default": "", "description": "Azure OpenAI Endpoint / Azure OpenAI 终结点"}, "dish-ai-commit.providers.azureOpenai.apiVersion": {"type": "string", "default": "", "description": "Azure OpenAI API Version / Azure OpenAI API 版本"}, "dish-ai-commit.providers.azureOpenai.orgId": {"type": "string", "default": "", "description": "Azure OpenAI Organization ID / Azure OpenAI 组织 ID"}, "dish-ai-commit.providers.cloudflare.apiKey": {"type": "string", "default": "", "description": "Cloudflare API Key / Cloudflare API 密钥"}, "dish-ai-commit.providers.cloudflare.accountId": {"type": "string", "default": "", "description": "Cloudflare Account ID / Cloudflare 账户 ID"}, "dish-ai-commit.providers.vertexai.projectId": {"type": "string", "default": "", "description": "Vertex AI Project ID / Vertex AI 项目 ID"}, "dish-ai-commit.providers.vertexai.location": {"type": "string", "default": "", "description": "Vertex AI Location / Vertex AI 位置"}, "dish-ai-commit.providers.vertexai.apiEndpoint": {"type": "string", "default": "", "description": "Optional. The base Vertex AI endpoint to use for the request. / 可选。用于请求的 Vertex AI 端点。"}, "dish-ai-commit.providers.vertexai.googleAuthOptions": {"type": "string", "default": "", "description": "Optional. JSON string of GoogleAuthOptions for authentication. / 可选。用于身份验证的 GoogleAuthOptions 的 JSON 字符串。"}, "dish-ai-commit.providers.groq.apiKey": {"type": "string", "default": "", "description": "Groq API Key / Groq API 密钥"}, "dish-ai-commit.features.codeAnalysis.diffTarget": {"type": "string", "default": "all", "description": "Specify the target for git diff: 'staged' for staged changes, 'all' for all changes. / 指定 git diff 的目标：'staged' 表示暂存区的更改，'all' 表示所有更改。", "enum": ["staged", "all"]}, "dish-ai-commit.features.codeAnalysis.simplifyDiff": {"type": "boolean", "default": false, "description": "Enable diff content simplification (Warning: Enabling this feature may result in less accurate commit messages) / 启用差异内容简化 (警告：启用此功能可能导致提交信息不够准确)"}, "dish-ai-commit.features.commitFormat.enableEmoji": {"type": "boolean", "default": true, "description": "Use emoji in commit messages / 在提交信息中使用表情符号"}, "dish-ai-commit.features.commitFormat.enableMergeCommit": {"type": "boolean", "default": false, "description": "Allow merging changes from multiple files into a single commit message / 允许将多个文件的更改合并为单个提交信息"}, "dish-ai-commit.features.commitFormat.enableBody": {"type": "boolean", "default": true, "description": "Include body content in commit messages (if disabled, only the subject line will be generated) / 在提交信息中包含主体内容（如果禁用，将仅生成标题行）"}, "dish-ai-commit.features.commitFormat.enableLayeredCommit": {"type": "boolean", "default": false, "description": "Generate layered commit messages with global summary and per-file details / 生成分层提交信息，包含全局摘要和每个文件的详细描述"}, "dish-ai-commit.features.commitMessage.systemPrompt": {"type": "string", "default": "", "description": "Custom system prompt for commit message generation / 提交信息生成的自定义系统提示语"}, "dish-ai-commit.features.commitMessage.useRecentCommitsAsReference": {"type": "boolean", "default": false, "description": "Use recent commits as a reference for generating commit messages / 使用最近的提交作为生成提交信息的参考"}, "dish-ai-commit.features.weeklyReport.systemPrompt": {"type": "string", "default": "", "description": "Custom system prompt for weekly report generation / 生成周报的自定义系统提示语"}, "dish-ai-commit.features.codeReview.systemPrompt": {"type": "string", "default": "", "description": "Custom system prompt for code review / 代码审查的自定义系统提示语"}, "dish-ai-commit.features.branchName.systemPrompt": {"type": "string", "default": "", "description": "Custom system prompt for branch name generation / 分支名称生成的自定义系统提示语"}, "dish-ai-commit.features.prSummary.baseBranch": {"type": "string", "default": "origin/main", "description": "Base branch for comparing commits to generate PR summary / 用于比较提交以生成PR摘要的基础分支"}, "dish-ai-commit.features.prSummary.headBranch": {"type": "string", "default": "HEAD", "description": "Head branch for comparing commits to generate PR summary / 用于比较提交以生成PR摘要的头部分支"}, "dish-ai-commit.features.prSummary.systemPrompt": {"type": "string", "default": "", "description": "Custom system prompt for PR summary generation / PR摘要生成的自定义系统提示语"}, "dish-ai-commit.features.prSummary.commitLogLimit": {"type": "number", "default": 20, "description": "The maximum number of commit logs to fetch for SVN when no specific range is provided. / 当未提供特定范围时，为 SVN 获取提交日志的最大数量。"}}}, "menus": {"scm/title": [{"command": "dish-ai-commit.generateCommitMessage", "when": "scmProvider =~ /(git|svn)/", "group": "navigation@-3"}, {"command": "dish-ai-commit.generateCommitMessage", "when": "scmProvider =~ /(git|svn)/", "group": "2_z_dish@1"}, {"command": "dish-ai-commit.generateWeeklyReport", "when": "scmProvider =~ /(git|svn)/", "group": "2_z_dish@2"}, {"command": "dish-ai-commit.reviewCode", "when": "scmProvider =~ /(git|svn)/", "group": "2_z_dish@3"}, {"command": "dish-ai-commit.generateBranchName", "when": "scmProvider == git", "group": "2_z_dish@4"}, {"command": "dish-ai-commit.generatePRSummary", "when": "scmProvider == git", "group": "2_z_dish@6"}, {"command": "dish-ai-commit.selectModel", "when": "scmProvider =~ /(git|svn)/", "group": "2_z_dish@5"}], "scm/resourceState/context": [{"command": "dish-ai-commit.generateCommitMessage", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@1"}, {"command": "dish-ai-commit.reviewCode", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@2"}, {"command": "dish-ai-commit.generateWeeklyReport", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@3"}, {"command": "dish-ai-commit.generateBranchName", "when": "config.git.enabled && scmProvider == git && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@4"}, {"command": "dish-ai-commit.generatePRSummary", "when": "config.git.enabled && scmProvider == git && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@6"}, {"command": "dish-ai-commit.selectModel", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "2_z_dish@5"}], "scm/resourceFolder/context": [{"command": "dish-ai-commit.generateCommitMessage", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}, {"command": "dish-ai-commit.reviewCode", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}, {"command": "dish-ai-commit.generateWeeklyReport", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}, {"command": "dish-ai-commit.generateBranchName", "when": "config.git.enabled && scmProvider == git && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}, {"command": "dish-ai-commit.generatePRSummary", "when": "config.git.enabled && scmProvider == git && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}, {"command": "dish-ai-commit.selectModel", "when": "((config.svn.enabled && scmProvider == svn) || (config.git.enabled && scmProvider == git)) && scmResourceGroup != unversioned && scmResourceGroup != external && scmResourceGroup != conflicts && scmResourceGroup != remotechanges", "group": "inline"}], "commandPalette": [{"command": "dish-ai-commit.selectModel"}, {"command": "dish-ai-commit.generateWeeklyReport"}, {"command": "dish-ai-commit.generateCommitMessage"}, {"command": "dish-ai-commit.generateBranchName"}, {"command": "dish-ai-commit.generatePRSummary"}, {"command": "dish-ai-commit.reviewCode"}]}}, "scripts": {"vscode:prepublish": "npm run vscode:prepublish:esbuild", "compile": "npm run compile:webview && tsc -p ./", "compile:webview": "cd src/webview-ui && pnpm install && pnpm run build", "watch:webview": "cd src/webview-ui && pnpm run dev", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "prepare": "husky", "commit": "git add . && git-cz", "release": "standard-version --preset gitmoji-config -i CHANGELOG.zh-CN.md", "changelog": "conventional-changelog -i CHANGELOG.md -s -r 0", "update-config": "ts-node ./src/scripts/update-config.ts", "package": "rm -rf node_modules && rm -rf src/webview-ui/node_modules && npm install && vsce package", "publish": "rm -rf node_modules && rm -rf src/webview-ui/node_modules && npm install && vsce publish && npx ovsx publish -p $OVSX_PAT", "publish:pre": "rm -rf node_modules && rm -rf src/webview-ui/node_modules && npm install && vsce publish --pre-release && npx ovsx publish --pre-release -p $OVSX_PAT", "compile:esbuild": "npm run check-types && ts-node esbuild.ts", "check-types": "tsc --noEmit", "watch:esbuild": "npm-run-all -p watch:esbuild-bundle watch:esbuild-tsc", "watch:esbuild-bundle": "ts-node esbuild.ts --watch", "watch:esbuild-tsc": "tsc --noEmit --watch --project tsconfig.json", "vscode:prepublish:esbuild": "npm run compile:webview && npm run compile:esbuild"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@stylistic/eslint-plugin-ts": "^4.4.1", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/node": "~24.0.0", "@types/uuid": "^10.0.0", "@types/vscode": "^1.90.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@typescript/native-preview": "7.0.0-dev.20250610.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "~3.3.2", "c8": "^10.1.3", "commitizen": "^4.3.1", "commitlint-config-gitmoji": "^2.3.1", "conventional-changelog-gitmoji-config": "^1.5.2", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.4.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.0", "npm-run-all": "^4.1.5", "standard-version": "^9.5.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.3"}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@baiducloud/qianfan": "^0.2.3", "@google-cloud/aiplatform": "^4.2.0", "@google-cloud/vertexai": "^1.10.0", "@google/genai": "^1.11.0", "@mistralai/mistralai": "^1.7.3", "@qdrant/js-client-rest": "^1.14.1", "groq-sdk": "^0.26.0", "inversify": "^7.5.2", "ollama": "^0.5.16", "openai": "^5.3.0", "tiktoken": "^1.0.21", "tree-sitter-wasms": "^0.1.11", "web-tree-sitter": "^0.22.6"}, "overrides": {"glob": "10.3.10", "brace-expansion": "2.0.1"}, "bundleDependencies": ["inversify", "ollama", "openai"], "resolutions": {"@types/node": "16.x"}, "license": "MIT", "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "lint-staged": {"src/**/*.{ts}": "npm run lint"}, "commitlint": {"extends": ["<PERSON><PERSON><PERSON><PERSON>"], "rules": {"header-max-length": [2, "always", 108], "type-empty": [2, "never"]}}, "packageManager": "pnpm@10.12.1"}